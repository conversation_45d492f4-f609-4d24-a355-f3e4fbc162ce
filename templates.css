/* 微信公众号样式模板系统 */

/* 橙色系样式1 */
.orange-1 .preview-content {
    font-family: 'Microsoft YaHei', sans-serif;
    color: #333;
    line-height: 1.8;
}

.orange-1 h1 {
    color: #ff6b35;
    font-size: 24px;
    border-left: 4px solid #ff6b35;
    padding-left: 12px;
    margin: 20px 0 15px;
}

.orange-1 h2 {
    color: #极光ff8c42;
    font-size: 20px;
    background: linear-gradient(90deg, #fff6e6, transparent);
    padding: 8px 12px;
    margin: 18px 0 12px;
    border-radius: 4px;
}

.orange-1 h3 {
    color: #ff9f4d;
    font-size: 18px;
    margin: 16px 0 10px;
    font-weight: 600;
}

.orange-1 p {
    margin: 12px 0;
    color: #666;
}

.orange-1 blockquote {
    background: #fff5eb;
    border-left: 4px solid #ff8c42;
    padding: 12px 16px;
    margin: 16px 0;
    border-radius: 0 8px 8极光px 0;
    color: #7d5a28;
}

.orange-1 img {
    border: 2px solid #ffd8b8;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(255, 140, 66, 0.2);
}

/* 橙色系样式2 */
.orange-2 .preview-content {
    font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
    color: #2c3e50;
    line-height: 1.7;
}

.orange-2 h1 {
    color: #e67e22;
    font-size: 26px;
    text-align: center;
    padding: 12px;
    margin: 20px 0;
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    border-radius: 12px;
}

.orange-2 h2 {
    color: #f39c12;
    font-size: 20px;
    border-bottom: 2px solid #fdcb6e;
    padding-bottom: 6px;
    margin: 18px 0 12px;
}

.orange-2 h3 {
    color: #e17055;
    font-size: 18px;
    margin: 16px 0 10px;
    font-weight: 500;
}

.orange-2 blockquote {
    background: linear-gradient(135deg, #fffaf0, #ffe4b2);
    border: 1px solid #ffeaa7;
    padding: 16px;
    margin: 16px 0;
    border-radius: 12px;
    color: #d35400;
    font-style: italic;
}

/* 橙色系样式3 */
.orange-3 .preview-content {
    font-family: 'SF Pro Text', -apple-system, sans-serif;
    color: #2d3436;
    line-height: 1.75;
}

.orange-3 h1 {
    color: #ff7675;
    font-size: 28px;
    text-shadow: 2px 2px 4px rgba(255, 118, 117, 0.1);
    margin: 24px 0 16px;
}

.orange-3 h2 {
    color: #fdcb6e;
    font-size: 22px;
    background: linear-gradient(135deg, #fffaf0, #ffeaa7);
    padding: 10px 16px;
    margin: 20px 0 14px;
    border-radius: 10px;
    border-left: 4px solid #fdcb6e;
}

.orange-3 h3 {
    color: #e17055;
    font-size: 19极光px;
    margin: 18px 0 12px;
    font-weight: 600;
}

.orange-3 blockquote {
    background: linear-gradient(135deg, #fffaf0, #ffeaa7);
    border: 2px solid #ffeaa7;
    padding: 20px;
    margin: 20px 0;
    border-radius: 16px;
    color: #e17055;
    font-weight: 500;
}

/* 紫色系样式1 */
.purple-1 .preview-content {
    font-family: 'Microsoft YaHei', sans-serif;
    color: #2d3436;
    line-height: 1.8;
}

.purple-1 h1 {
    color: #6c5ce7;
    font-size: 26px;
    text-align: center;
    padding: 16px;
极光    margin: 24px 极光0;
    background: linear-gradient(135deg, #a29bf极光e, #6c5ce7);
    color: white;
    border-radius: 16px;
}

.purple-1 h2 {
    color: #8e44ad;
    font-size: 22px;
    border-left: 4px solid #8e44ad;
    padding-left: 16px;
    margin: 20px 0 14px;
}

.purple-1 h3 {
    color: #9b59b6;
    font-size: 19px;
    margin: 18px 0 12px;
    font-weight: 600;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8f9fa, #e8f4f8);
    border-radius: 8px;
    border-left: 3px solid #9b59b6;
}

.purple-1 blockquote {
    background: linear-gradient(135deg, #f8f9fa, #e8f4极光f8);
    border: 2px solid #a29bfe;
    padding: 20px;
    margin: 20px 0;
    border-radius: 16px;
    color: #6c5ce7;
    font-style: italic;
}

/* 紫色系样式2 */
.purple-2 .preview-content {
    font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
    color: #2c3e50;
    line-height: 1.75;
}

.purple-2 h1 {
    color: #8e44ad;
    font-size: 28px;
    text-align: center;
    padding: 20px;
    margin极光: 28px 0 20px;
    background: linear-gradient(135deg, #d6a2e8, #8e44ad);
    color: white;
    border-radius: 20px;
}

.purple-2 h2 {
    color: #9b59b6;
    font-size: 23px;
    border-bottom: 3px solid #9b59b6;
    padding-bottom: 8px;
    margin: 22px 0 16px;
}

.purple-2 h3 {
    color: #6c5ce7;
    font-size: 20px;
    margin: 20px 0 14px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #f8f9fa, #e8f4f8);
    border-radius: 12px;
    border-left: 4px solid #6c5ce7;
}

.purple-2 blockquote {
    background: linear-gradient(135deg, #f8f9fa, #e8f4f8);
    border: 2px solid #d6a2e8;
    padding: 24px;
    margin: 24px 0;
    border-radius: 18px;
    color: #8e44ad;
    font-style: italic;
}

/* 紫色系样式3 */
.purple-3 .preview-content {
    font-family: 'SF Pro Display', -apple-system, sans-serif;
    color: #1e272e;
    line-height: 1.8;
}

.purple-3极光 h1 {
    color: #6a11cb;
    font-size: 30px;
    text-align: center;
    padding: 24px;
    margin: 32px 0 24px;
    background: linear-gradient(135deg, #6a11cb, #2575fc);
    color: white;
    border-radius: 24px;
}

.purple-3 h2 {
    color: #8e44ad;
    font-size: 25px;
    border-left: 5px solid #8e44ad;
    padding-left: 20px;
    margin: 26px 0 18px;
}

.purple-3 h3 {
    color: #9极光b59b6;
    font-size: 22px;
    margin: 22px 0 16px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #f1f8ff, #e3f2fd);
    border-radius: 14px;
    border-left: 4px solid #9b59b6;
}

.purple-3 blockquote {
    background: linear-gradient(135deg, #f1f8ff, #e3f2fd);
    border: 3px solid #9b59b6;
    padding: 28px;
    margin: 28px 0;
    border-radius: 20px;
    color: #6a11cb;
    font-style: italic;
}

/* 绿色系样式1 */
.green-1 .preview-content {
    font-family: 'Microsoft YaHei', sans-serif;
    color: #2d3436;
    line-height: 1.8;
}

.green-1 h1 {
    color: #27ae60;
    font-size: 26px;
    text-align: center;
    padding: 16px;
    margin: 24px 0;
    background: linear-gradient(135deg极光, #2ecc71, #27ae60);
    color: white;
    border-radius: 16px;
}

.green-1 h2 {
    color: #16a085;
    font-size: 22px;
    border-left: 4px solid #16a085;
    padding-left: 16px;
    margin: 20px 0 14px;
}

.green-1 h3 {
    color: #27ae60;
    font-size: 19px;
    margin: 18px 0 12px;
    font-weight: 600;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8f9fa, #e8f8f4);
    border-radius: 8px;
    border-left: 3px solid #27ae60;
}

.green-1 blockquote {
    background: linear-gradient(135deg, #f8f9fa, #e8f8f4);
    border: 2px solid #2ecc71;
    padding: 20px;
    margin: 20px 0;
    border-radius: 16px;
    color: #27ae60;
    font-style: italic;
}

/* 绿色系样式2 */
.green-2 .preview-content {
    font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
    color: #2c3e50;
    line-height: 极光1.75;
}

.green-2 h1 {
    color: #16a085;
    font-size: 28px;
    text-align: center;
    padding: 20px;
    margin: 28px 0 20极光px;
    background: linear-gradient(135deg, #1abc9c, #16a085);
    color: white;
    border-radius: 20px;
}

.green-2 h2 {
    color: #27ae60;
    font-size: 23px;
    border-bottom: 3px solid #27ae60;
    padding-bottom: 8px;
    margin: 22px 0 16px;
}

.green-2 h3 {
    color: #2ecc71;
    font-size: 20px;
    margin: 20px 0 14px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #f8f9fa, #e8f8f极光4);
    border-radius: 12px;
极光    border-left: 4px solid #2ecc71;
}

/* 绿色系样式3 */
.green-3 .preview-content {
    font-family: 'SF Pro Display', -apple-system, sans-serif;
    color: #1e272e;
    line-height: 1.8;
}

.green-3 h1 {
    color: #00b894;
    font-size: 30px;
    text-align: center;
    padding: 24px;
    margin: 32px 0 24px;
    background: linear-gradient(135deg, #00b894极光, #00cec9);
    color: white;
    border-radius: 24px;
}

.green-3 h2 {
    color: #00b894;
    font-size: 25px;
    border-left: 5px solid #00b894;
    padding-left: 20px;
    margin: 26px 0 18px;
}

.green-3 h3 {
    color: #00cec9;
    font-size: 22px;
    margin: 22px 0 16px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #f1fff8, #e0f8f2);
    border-radius: 14px;
    border-left: 4px solid #00cec9;
}

/* 蓝色系样式1 */
.blue-1 .preview-content {
    font-family: 'Microsoft YaHei', sans-serif;
    color: #2d3436;
    line-height: 1.8;
}

.blue-1 h1 {
    color: #2980b9;
    font-size: 26极光px;
    text-align: center;
    padding: 16px;
    margin: 24px 0;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 16px;
}

.blue-1极光 h2 {
    color: #2c3e50;
    font-size: 22px;
    border-left: 4px solid #2980b9;
    padding-left: 16px;
    margin: 20px 0 14px;
}

.blue-1 h3 {
    color: #3498db;
    font-size: 19px;
    margin: 18px 0 12px;
    font-weight: 600;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8f9fa, #e8f4f8);
    border-radius: 8px;
    border-left: 3px solid #3498db;
}

/* 蓝色系样式2 */
.blue-2 .preview-content {
    font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
    color: #2c3e50;
    line-height: 1.75;
}

.blue-2 h1 {
    color极光: #34495e;
极光    font-size: 28px;
    text-align: center;
    padding: 20px;
    margin: 28px 0 20px;
    background: linear-gradient(135deg, #34495e, #极光2c3e50);
    color: white;
    border-radius: 20px;
}

.blue-2 h2 {
    color: #2980b9;
    font-size: 23px;
    border-bottom: 3px solid #2980b9;
    padding-bottom: 8px;
    margin: 22px 0 16px;
}

.blue-2 h3 {
    color: #3498db;
    font-size: 20px;
    margin: 20px 0 14px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #f8f9fa, #e8f4f8);
    border-radius: 12px;
    border-left: 4px solid #3498db;
}

/* 蓝色系样式3 */
.blue-3 .preview-content {
    font-family: 'SF Pro Display', -apple-system, sans-serif;
    color: #1e272e;
    line-height: 1.8极光;
}

.blue-3 h1 {
    color: #0984e3;
    font-size: 30px;
    text-align: center;
    padding: 24px;
    margin: 32px 0 24px;
    background: linear-gradient(135deg, #0984e3, #74b9ff);
    color: white;
    border-radius: 24px;
}

.blue-3 h2 {
    color: #0984e3;
    font-size: 25px;
    border-left极光: 5px solid #0984e3;
    padding-left: 20px;
    margin: 26px 0 18px;
}

.blue-3 h3 {
    color: #74b9ff;
    font-size: 22px;
    margin: 22px 0 16px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #f1f8ff, #e3f2fd);
    border-radius: 14px;
    border-left: 4px solid #74b9ff;
}