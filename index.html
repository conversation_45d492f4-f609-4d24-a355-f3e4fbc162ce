<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信公众号样式生成工具</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header class="header">
        <h1>微信公众号样式生成工具</h1>
        <p>Markdown输入 → 样式选择 → 一键复制</p>
    </header>

    <main class="main-container">
        <!-- 编辑器区域 -->
        <section class="editor-section">
            <h2>Markdown编辑器</h2>
            <div class="editor-toolbar">
                <button class="tool-btn" title="加粗">B</button>
                <button class="tool-btn" title="斜体">I</button>
                <button class="tool-btn" title="标题">H</button>
                <button class="tool-btn" title="引用">❝</button>
                <button class="tool-btn" title="列表">•</button>
                <button class="tool-btn" title="上传图片" id="uploadImage">📷</button>
            </div>
            <textarea id="markdownInput" placeholder="在这里输入Markdown内容...&#10;&#10;示例：&#10;# 一级标题&#10;## 二级标题&#10;### 三级标题&#10;&#10;这是段落文本&#10;&#10;> 这是引用内容&#10;&#10;- 列表项1&#10;- 列表项2"></textarea>
        </section>

        <!-- 样式选择区域 -->
        <section class="style-section">
            <h2>选择样式模板</h2>
            <div class="style-categories">
                <div class="category" data-category="orange">
                    <h3>橙色系</h3>
                    <div class="style-options"></div>
                </div>
                <div class="category" data-category="purple">
                    <h3>紫色系</h3>
                    <div class="style-options"></div>
                </div>
                <div class="category" data-category="green">
                    <h3>绿色系</h3>
                    <div class="style-options"></div>
                </div>
                <div class="category" data-category="blue">
                    <h3>蓝色系</h3>
                    <div class="style-options"></div>
                </div>
            </div>
        </section>

        <!-- 预览区域 -->
        <section class="preview-section">
            <h2>实时预览</h2>
            <div class="preview-container" id="previewContainer">
                <div class="preview-placeholder">
                    <p>选择样式后这里将显示预览效果</p>
                </div>
            </div>
            <div class="preview-actions">
                <button id="copyBtn" class="copy-btn" disabled>复制到剪贴板</button>
            </div>
        </section>
    </main>

    <!-- 图片上传模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <h3>上传图片</h3>
            <input type="file" id="imageUpload" accept="image/*,.gif" multiple>
            <div class="image-options">
                <label>边框颜色: <input type="color" id="borderColor" value="#cccccc"></label>
                <label>边框样式: 
                    <select id="borderStyle">
                        <option value="rounded">圆角</option>
                        <option value="square">直角</option>
                    </select>
                </label>
            </div>
            <div class="modal-actions">
                <button id="confirmUpload">插入图片</button>
                <button id="cancelUpload">取消</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>