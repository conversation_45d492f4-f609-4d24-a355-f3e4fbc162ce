// 全局变量
let currentStyle = null;
let markdownContent = '';

// DOM 元素
const markdownInput = document.getElementById('markdownInput');
const previewContainer = document.getElementById('previewContainer');
const copyBtn = document.getElementById('copyBtn');
const uploadImageBtn = document.getElementById('uploadImage');
const imageModal = document.getElementById('imageModal');
const imageUpload = document.getElementById('imageUpload');
const borderColor = document.getElementById('borderColor');
const borderStyle = document.getElementById('borderStyle');
const confirmUpload = document.getElementById('confirmUpload');
const cancelUpload = document.getElementById('cancelUpload');

// 初始化函数
function init() {
    loadStyleTemplates();
    setupEventListeners();
    setupDefaultStyles();
}

// 加载样式模板
function loadStyleTemplates() {
    const categories = ['orange', 'purple', 'green', 'blue'];
    
    categories.forEach(category => {
        const container = document.querySelector(`[data-category="${category}"] .style-options`);
        
        // 为每个色系创建3个样式选项
        for (let i = 1; i <= 3; i++) {
            const styleOption = document.createElement('div');
            styleOption.className = 'style-option';
            styleOption.classList.add(`${category}-style`);
            styleOption.textContent = `样式 ${i}`;
            styleOption.dataset.style = `${category}-${i}`;
            
            styleOption.addEventListener('click', () => {
                selectStyle(`${category}-${i}`);
            });
            
            container.appendChild(styleOption);
        }
    });
}

// 设置事件监听器
function setupEventListeners() {
    // Markdown 输入监听
    markdownInput.addEventListener('input', (e) => {
        markdownContent = e.target.value;
        updatePreview();
    });

    // 复制按钮
    copyBtn.addEventListener('click', copyToClipboard);

    // 图片上传相关
    uploadImageBtn.addEventListener('click', openImageModal);
    cancelUpload.addEventListener('click', closeImageModal);
    confirmUpload.addEventListener('click', handleImageUpload);
}

// 设置默认样式
function setupDefaultStyles() {
    // 添加一些基础CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .preview-content h1 { font-size: 2em; margin: 0.67em 0; }
        .preview-content h2 { font-size: 1.5em; margin: 0.83em 0; }
        .preview-content h3 { font-size: 1.17em; margin: 1em 0; }
        .preview-content p { margin: 1em 0; }
        .preview-content blockquote { 
            border-left: 4px solid #ddd; 
            margin: 1em 0; 
            padding-left: 1em; 
            color: #666; 
        }
        .preview-content ul, .preview-content ol { 
            margin: 1em 0; 
            padding-left: 2em; 
        }
        .preview-content img { 
            max-width: 100%; 
            height: auto; 
            margin: 1em 0;
        }
    `;
    document.head.appendChild(style);
}

// 选择样式
function selectStyle(styleId) {
    currentStyle = styleId;
    document.querySelectorAll('.style-option').forEach(btn => {
        btn.style.borderColor = btn.dataset.style === styleId ? '#667eea' : 'transparent';
    });
    updatePreview();
    copyBtn.disabled = false;
}

// 更新预览
function updatePreview() {
    if (!markdownContent.trim()) {
        previewContainer.innerHTML = '<div class="preview-placeholder"><p>选择样式后这里将显示预览效果</p></div>';
        return;
    }

    const htmlContent = markdownToHtml(markdownContent);
    previewContainer.innerHTML = `
        <div class="preview-content ${currentStyle || ''}">
            ${htmlContent}
        </div>
    `;
    
    // 应用当前选择的样式
    applyStyle(currentStyle);
}

// Markdown 转 HTML 的简单实现
function markdownToHtml(markdown) {
    return markdown
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
        .replace(/^- (.*$)/gim, '<ul><li>$1</li></ul>')
        .replace(/\n<ul>/g, '</ul><ul>')
        .replace(/<\/ul><ul>/g, '')
        .replace(/\n/g, '<br>');
}

// 应用样式
function applyStyle(styleId) {
    // 这里后续会根据不同的styleId应用不同的CSS样式
    console.log('应用样式:', styleId);
}

// 复制到剪贴板
function copyToClipboard() {
    if (!currentStyle) return;

    const previewContent = previewContainer.querySelector('.preview-content');
    if (!previewContent) return;

    const html = previewContent.innerHTML;
    const text = previewContent.textContent;

    const blob = new Blob([html], { type: 'text/html' });
    const blobText = new Blob([text], { type: 'text/plain' });

    const data = [
        new ClipboardItem({
            'text/html': blob,
            'text/plain': blobText
        })
    ];

    navigator.clipboard.write(data).then(() => {
        alert('内容已复制到剪贴板！');
    }).catch(err => {
        console.error('复制失败:', err);
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = html;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('内容已复制到剪贴板！');
    });
}

// 图片上传相关功能
function openImageModal() {
    imageModal.style.display = 'block';
}

function closeImageModal() {
    imageModal.style.display = 'none';
    imageUpload.value = '';
}

function handleImageUpload() {
    const files = imageUpload.files;
    if (files.length === 0) return;

    const file = files[0];
    if (file.size > 10 * 1024 * 1024) {
        alert('文件大小不能超过10MB');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const imageUrl = e.target.result;
        const isRounded = borderStyle.value === 'rounded';
        const borderColorValue = borderColor.value;
        
        const imageMarkdown = `![图片](${imageUrl})`;
        insertAtCursor(markdownInput, imageMarkdown);
        
        closeImageModal();
    };
    reader.readAsDataURL(file);
}

// 在光标位置插入文本
function insertAtCursor(textarea, text) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const before = textarea.value.substring(0, start);
    const after = textarea.value.substring(end);
    
    textarea.value = before + text + after;
    textarea.selectionStart = textarea.selectionEnd = start + text.length;
    textarea.focus();
    
    // 触发input事件更新预览
    const event = new Event('input', { bubbles: true });
    textarea.dispatchEvent(event);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);

// 关闭模态框的点击事件
imageModal.addEventListener('click', (e) => {
    if (e.target === imageModal) {
        closeImageModal();
    }
});