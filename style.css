/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem;
    margin-bottom: 2rem;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主容器布局 */
.main-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 各区域基础样式 */
.editor-section,
.style-section,
.preview-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.editor-section h2,
.style-section h2,
.preview-section h2 {
    margin-bottom: 1rem;
    color: #2c3e50;
    border-bottom: 2px solid #eee;
    padding-bottom: 0.5rem;
}

/* 编辑器样式 */
.editor-toolbar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tool-btn {
    padding: 0.5rem;
    border: 1px solid #ddd;
    background: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.tool-btn:hover {
    background: #e9ecef;
    border-color: #ccc;
}

#markdownInput {
    width: 100%;
    height: 400px;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
}

#markdownInput:focus {
    outline: none;
    border-color: #667eea;
}

/* 样式选择区域 */
.style-categories {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.category h3 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.style-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.style-option {
    padding: 0.75rem;
    border: 2px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.style-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 预览区域 */
.preview-container {
    min-height: 400px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 1rem;
    background: #fafafa;
}

.preview-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-style: italic;
}

.preview-actions {
    margin-top: 1rem;
    text-align: center;
}

.copy-btn {
    padding: 0.75rem 2rem;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s;
}

.copy-btn:hover:not(:disabled) {
    background: #218838;
}

.copy-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 2rem;
    border-radius: 12px;
    min-width: 400px;
}

.modal-content h3 {
    margin-bottom: 1rem;
}

.image-options {
    margin: 1rem 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.image-options label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .editor-section,
    .style-section,
    .preview-section {
        margin-bottom: 1rem;
    }
}

/* 基础色系占位样式 */
.orange-style { background: linear-gradient(135deg, #ff7e5f, #feb47b); color: white; }
.purple-style { background: linear-gradient(135deg, #4776E6, #8E54E9); color: white; }
.green-style { background: linear-gradient(135deg, #56ab2f, #a8e063); color: white; }
.blue-style { background: linear-gradient(135deg, #3494E6, #EC6EAD); color: white; }